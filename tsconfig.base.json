{
	"compilerOptions": {
		"allowImportingTsExtensions": true,
		"allowJs": true,
		"allowSyntheticDefaultImports": true,
		"esModuleInterop": true,
		// "exactOptionalPropertyTypes": true,
		"isolatedModules": true,
		"jsx": "preserve",

		"module": "ESNext",
		"moduleResolution": "bundler",
		"noEmit": true,
		"noFallthroughCasesInSwitch": true,
		"noPropertyAccessFromIndexSignature": true,
		"noUncheckedIndexedAccess": true,
		"lib": ["ESNext", "DOM"],
		"skipLibCheck": true,
		"sourceMap": true,
		"strict": true,
		"target": "ESNext"
	}
}
