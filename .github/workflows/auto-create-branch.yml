name: Create Branch for Todo Task

on:
  issues:
    types: [opened]
  workflow_dispatch:

env:
  BASE_BRANCH: main

jobs:
  create-branch-for-task:
    if: contains(github.event.issue.labels.*.name, 'C-todo')
    runs-on: ubuntu-latest

    steps:
      - name: Checkout main
        uses: actions/checkout@v4
        with:
          ref: ${{ env.BASE_BRANCH }}

      - name: Set up Git config
        run: |
          git config --global user.name "${{ github.actor }}"
          git config --global user.email "${{ github.actor }}@users.noreply.github.com"

      - name: Generate branch name
        run: |
          ISSUE_NUMBER=${{ github.event.issue.number }}
          ISSUE_TITLE="${{ github.event.issue.title }}"
          SLUG=$(echo "$ISSUE_TITLE" \
           | sed 's/\bTask\b//g' \
           | tr '[:upper:]' '[:lower:]' \
           | sed -E 's/[^a-z0-9]+/-/g' \
           | sed -E 's/^-|-$//g' | cut -c1-25)
          BRANCH_NAME="${ISSUE_NUMBER}-${SLUG}"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV

      - name: Create and push branch
        run: |
          git fetch origin ${{ env.BASE_BRANCH }}
          if git ls-remote --heads origin "$BRANCH_NAME" | grep -q "$BRANCH_NAME"; then
            echo "Branch $BRANCH_NAME already exists. Skipping."
          else
            git checkout -b "$BRANCH_NAME" origin/${{ env.BASE_BRANCH }}
            git push origin "$BRANCH_NAME"
          fi

      - name: Comment on the issue
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.createComment({
              ...context.repo,
              issue_number,
              body: `🚀 自动为任务创建了分支：\`${process.env.BRANCH_NAME}\`。\n请在此分支下开发并提交 PR！`
            });
