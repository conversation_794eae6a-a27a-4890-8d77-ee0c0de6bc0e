name: 📝 Todo Task
description: 用于创建开发/实现任务，会自动添加标签并创建分支
title: "Task: "
labels: ["todo task"]
assignees:
  - your-github-username # 可选

body:
  - type: markdown
    attributes:
      value: |
        详细描述该任务的内容、背景和目标。

  - type: textarea
    id: description
    attributes:
      label: 📋 任务描述
      description: 说明任务需求与目标
      placeholder: |
        示例：
        需求：
          网站需要实现用户鉴权功能
        目标：
          - [ ] 实现用户鉴权页面
          - [ ] 如果已经登录，重定向到主页
          - [ ] 成功后跳转到主页

  - type: textarea
    id: steps
    attributes:
      label: ✅ 实施建议或步骤
      description: 如果你有想法或步骤，请列出来
      placeholder: |
        1. 添加路由
        2. 编写 controller
        3. 编写测试

  - type: textarea
    id: related
    attributes:
      label: 🔗 相关 issue/PR（如有）
      placeholder: "使用 # 引用 Issue/PR"

  - type: dropdown
    id: priority
    attributes:
      label: 🚦 优先级
      default: 1
      options:
        - 高
        - 中
        - 低
