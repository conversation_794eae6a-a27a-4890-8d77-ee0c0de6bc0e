# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a SolidJS web application for THCDB (TouHou Cloud Database), a music database platform. The project uses TypeScript and Vite, with a Rust backend server in the `../server` directory.

## Essential Commands

### Development

- `pnpm dev` or `pnpm start` - Start development server on port 3000
- `pnpm build` - Build for production
- `pnpm serve` - Preview production build
- `pnpm check` - Run Prettier format check
- `just lint` - Lint (oxlint + eslint)
- `just fix` - Auto fix issues
- `just fmt` - Format
- `just test` - Run tests

### Storybook

- `pnpm storybook` - Start Storybook dev server on port 6006
- `pnpm build-storybook` - Build Storybook

<!-- ### Testing

- Tests are configured with Vitest workspace (vitest.workspace.ts)
- Browser tests run with Playwright on Chromium
- DOM tests are in `test/dom/**/*.{test,vitest}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}`
- Library tests exclude DOM tests and run in Node environment -->

### Environment Setup

- **Required**: Set `VITE_SERVER_URL` environment variable - points to the remote server for API calls and OpenAPI schema generation
- The dev server proxies `/api/*` requests to the server URL

## Architecture

### Tech Stack

- **Frontend**: SolidJS + TypeScript
- **Styling**: TailwindCSS v4 with custom design system
- **Routing**: TanStack Router with auto-code splitting
- **State Management**: TanStack Query for server state, SolidJS stores for client state
- **Forms**: Modular Forms + Valibot validation
- **I18n**: Lingui with solid integration
- **Build Tool**: Vite with custom plugins

### Project Structure

**Core Directories**:

- `src/api/` - API layer with OpenAPI-generated types and query functions
- `src/components/` - Reusable UI components organized by domain (common, form, dialog, etc.)
- `src/views/` - Page-level components and business logic
- `src/routes/` - TanStack Router route definitions
- `src/state/` - Global state management (i18n, theme, user, TanStack Query)
- `src/utils/` - Utility functions and adapters

**Component Architecture**:

- Components follow atomic design with stories for documentation
- Form components use Modular Forms with Valibot schemas
- Built on Kobalte primitives

**API Integration**:

- OpenAPI schema auto-generated from server
- API functions organized by domain (artist, song, release, user, etc.)

### Key Technologies

**Forms & Validation**:

- `@modular-forms/solid` for form state management
- `valibot` for schema validation
- Custom form components in `src/components/form/`

**Styling System**:

- TailwindCSS v4 with custom color palette and design tokens
- CSS custom properties for theming in `src/style/`
- Component-specific styles co-located

**Internationalization**:

- Lingui for i18n with macro support
- Translation files in `src/locale/en/` and `src/locale/zh-Hans/`
- Always run `just i18n` after adding new translatable content (requires Just CLI tool)

## Development Guidelines

### Code Style

- Uses ESLint with TypeScript strict rules + Oxlint
- Prettier for formatting with import sorting
- Prefer `Trans` component over `t` function for i18n
- Component files should export default and have co-located stories when appropriate

### Workspace Setup

- Monorepo with pnpm workspace
- Local `@thc/toolkit` package for shared utilities
- Uses path mapping via tsconfig for clean imports

### Build Process

- Vite build targets `esnext`
- Custom generate plugin creates constants and types from server OpenAPI schema
- TanStack Router generates route tree automatically

## Domain Knowledge

This is a music database application with entities for:

- **Artists** - Musicians/bands with profiles, memberships, and releases
- **Songs** - Individual tracks with credits, lyrics, and metadata
- **Releases** - Albums/EPs with track listings and cover art
- **Credits** - Roles and contributions (producer, vocalist, etc.)
- **Users** - User profiles, follows, and authentication

The application supports both English and Simplified Chinese localization.
