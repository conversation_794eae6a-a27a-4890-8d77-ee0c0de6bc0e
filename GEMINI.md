---
trigger: always_on
---

# Agent Guidelines for TouhouCloudDB Web Project

Create a todo list before you start editing.
When you feel you are done, check your todo list for unfinished items.

## Build & Development

- Development: `bun run dev`
- Build: `bun run build`
- Format code: `bun run format`

## Testing

- Run all tests: `bun run test`
- Run Vitest tests only: `bun run test::vite`
- Run Bun tests only: `bun run test::bun`
- Single test: `bun vitest run path/to/test.test.tsx`

## Code Style

- **Formatting**:
  - No semicolons
  - Use tabs (except YAML: 2 spaces)
  - Place each attribute on its own line in JSX/TSX

- **Imports**:
  - Follow order: third-party modules → root (~) → parent dirs (../) → current dir (./)
  - Import types with `import type` syntax

- **Types**:
  - Use strict TypeScript, avoid `any`, prefer explicit return types
  - Prefer using type over inferfaces

- **Naming**: PascalCase for components, camelCase for variables/functions

- **Path Alias**: Use `~/` prefix to import from src directory

- **HTML**: Use semantic html

## Code Reference

- We Use SolidJS, don't use react syntax
  - Don't destruct props

- Use Tailwind CSS for styling

- Use lingui-solid for i18n https://github.com/Azq2/js-lingui-solid
  - wrap JSX text in `Trans` component and string literal in `t` function,
    for example:

    ```tsx
    import { createEffect } from "solid-js";
    import { useLingui, Trans } from "@lingui-solid/solid/macro";

    const CurrentLocale = () => {
    const { t, i18n } = useLingui();

    createEffect(() => console.log(`Language chnaged: ${i18n().locale}`));

    return (
    	<span>
    		{t`Current locale`}: {i18n().locale}<br />
    		<Trans>
    		See for more info:
    		<a href="https://lingui.dev/introduction">official documentation</a>
    		</Trans>;
    	</span>
    );

    ```

## Error Handling

- Use `arktype` and `valibot` for data validation

- Prefer early returns over deep nesting
