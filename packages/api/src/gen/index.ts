/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export type paths = {
    "/{entity_type}/{id}/pending-correction": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["pending_correction"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/artist": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_many_artist"];
        put?: never;
        post: operations["create_artist"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/artist/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_artist_by_id"];
        put?: never;
        post: operations["upsert_artist_correction"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/artist/{id}/appearances": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_artist_apperances"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/artist/{id}/credits": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["get_artist_credits"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/artist/{id}/discographies": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_artist_discographies_by_type"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/artist/{id}/discographies/init": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_artist_discographies_init"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/artist/{id}/profile-image": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["upload_artist_profile_image"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/avatar": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["upload_avatar"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/correction/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["handle_correction"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/credit-role": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["create_credit_role"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/credit-role/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_credit_role_by_id"];
        put?: never;
        post: operations["upsert_credit_role_correction"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/credit-role/summary": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_many_credit_roles_summary"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/event": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_event_by_keyword"];
        put?: never;
        post: operations["create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/event/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_event_by_id"];
        put?: never;
        post: operations["upsert_correction"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/health_check": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["health_check"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/label": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_label_by_keyword"];
        put?: never;
        post: operations["create_label"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/label/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_label_by_id"];
        put?: never;
        post: operations["upsert_label_correction"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/languages": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["language_list"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/profile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["profile"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/profile-banner": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["upload_profile_banner"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/profile/{name}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["profile_with_name"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/profile/bio": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["update_bio"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/release": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_release_by_keyword"];
        put?: never;
        post: operations["create_release"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/release/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_release_by_id"];
        put?: never;
        post: operations["update_release"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/release/{id}/cover-art": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["upload_release_cover_art"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/sign-in": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["sign_in"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/sign-out": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["sign_out"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/sign-up": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["sign_up"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/song": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_song_by_keyword"];
        put?: never;
        post: operations["create_song"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/song-lyrics": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_one_song_lyrics"];
        put?: never;
        post: operations["create_song_lyrics"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/song-lyrics/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["update_song_lyrics"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/song-lyrics/many": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_many_song_lyrics"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/song/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_song_by_id"];
        put?: never;
        post: operations["update_song"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/tag": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_tag_by_keyword"];
        put?: never;
        post: operations["create_tag"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/tag/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["find_tag_by_id"];
        put?: never;
        post: operations["upsert_tag_correction"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/user-roles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["user_roles"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
};
export type webhooks = Record<string, never>;
export type components = {
    schemas: {
        AlternativeName: {
            /** Format: int32 */
            id: number;
            name: string;
        };
        Artist: {
            /** @description List of id of artist aliases */
            aliases?: number[];
            artist_type: components["schemas"]["ArtistType"];
            current_location?: components["schemas"]["Location"];
            end_date?: null | components["schemas"]["DateWithPrecision"];
            /** Format: int32 */
            id: number;
            links?: string[];
            localized_names?: components["schemas"]["LocalizedName"][];
            /** @description Groups list for individuals, member list for groups, */
            memberships?: components["schemas"]["Membership"][];
            name: string;
            /** @description Profile image of artist */
            profile_image_url?: string | null;
            start_date?: null | components["schemas"]["DateWithPrecision"];
            start_location?: components["schemas"]["Location"];
            /** @description Aliases without own page */
            text_aliases?: string[] | null;
        };
        ArtistReleaseArtist: {
            /** Format: int32 */
            id: number;
            name: string;
        };
        /** @enum {string} */
        ArtistType: "Solo" | "Multiple" | "Unknown";
        AuthCredential: {
            password: string;
            username: string;
        };
        CatalogNumber: {
            catalog_number: string;
            /** Format: int32 */
            label_id?: number | null;
        };
        /** @enum {string} */
        CorrectionType: "Create" | "Update" | "Delete";
        CreditRole: {
            description: string;
            /** Format: int32 */
            id: number;
            name: string;
            short_description: string;
        };
        CreditRoleRef: {
            /** Format: int32 */
            id: number;
            name: string;
        };
        CreditRoleSummary: {
            /** Format: int32 */
            id: number;
            name: string;
            short_description: string;
        };
        Data_Event: {
            data: {
                alternative_names?: components["schemas"]["AlternativeName"][];
                description?: string | null;
                end_date?: null | components["schemas"]["DateWithPrecision"];
                /** Format: int32 */
                id: number;
                name: string;
                short_description?: string | null;
                start_date?: null | components["schemas"]["DateWithPrecision"];
            };
            /** @enum {string} */
            status: "Ok";
        };
        Data_Option_i32: {
            data: null | number;
            /** @enum {string} */
            status: "Ok";
        };
        DataInitDiscography: {
            data: components["schemas"]["InitDiscography"];
            status: string;
        };
        DataOptionArtist: {
            data: null | components["schemas"]["Artist"];
            status: string;
        };
        DataOptionCreditRole: {
            data: null | components["schemas"]["CreditRole"];
            status: string;
        };
        DataOptionLabel: {
            data: null | components["schemas"]["Label"];
            status: string;
        };
        DataOptionRelease: {
            data: null | components["schemas"]["Release"];
            status: string;
        };
        DataOptionSong: {
            data: null | components["schemas"]["Song"];
            status: string;
        };
        DataOptionSongLyrics: {
            data: null | components["schemas"]["SongLyrics"];
            status: string;
        };
        DataOptionTag: {
            data: null | components["schemas"]["Tag"];
            status: string;
        };
        DataPaginatedAppearance: {
            data: components["schemas"]["Paginated_Discography"];
            status: string;
        };
        DataPaginatedCredit: {
            data: components["schemas"]["Paginated_Credit"];
            status: string;
        };
        DataPaginatedDiscography: {
            data: components["schemas"]["Paginated_Discography"];
            status: string;
        };
        DataUserProfile: {
            data: components["schemas"]["UserProfile"];
            status: string;
        };
        DataVecArtist: {
            data: components["schemas"]["Artist"][];
            status: string;
        };
        DataVecCreditRoleSummary: {
            data: components["schemas"]["CreditRoleSummary"][];
            status: string;
        };
        DataVecEvent: {
            data: components["schemas"]["Event"][];
            status: string;
        };
        DataVecLabel: {
            data: components["schemas"]["Label"][];
            status: string;
        };
        DataVecLanguage: {
            data: components["schemas"]["Language"][];
            status: string;
        };
        DataVecRelease: {
            data: components["schemas"]["Release"][];
            status: string;
        };
        DataVecSong: {
            data: components["schemas"]["Song"][];
            status: string;
        };
        DataVecSongLyrics: {
            data: components["schemas"]["SongLyrics"][];
            status: string;
        };
        DataVecTag: {
            data: components["schemas"]["Tag"][];
            status: string;
        };
        DataVecUserRole: {
            data: components["schemas"]["UserRoleEnum"][];
            status: string;
        };
        /** @enum {string} */
        DatePrecision: "Day" | "Month" | "Year";
        DateWithPrecision: {
            precision: components["schemas"]["DatePrecision"];
            /** Format: date */
            value: string;
        };
        EntityIdent: string;
        Event: {
            alternative_names?: components["schemas"]["AlternativeName"][];
            description?: string | null;
            end_date?: null | components["schemas"]["DateWithPrecision"];
            /** Format: int32 */
            id: number;
            name: string;
            short_description?: string | null;
            start_date?: null | components["schemas"]["DateWithPrecision"];
        };
        /** @enum {string} */
        HandleCorrectionMethod: "Approve" | "Reject";
        InitDiscography: {
            album: components["schemas"]["Paginated_Discography"];
            compilation: components["schemas"]["Paginated_Discography"];
            demo: components["schemas"]["Paginated_Discography"];
            ep: components["schemas"]["Paginated_Discography"];
            other: components["schemas"]["Paginated_Discography"];
            single: components["schemas"]["Paginated_Discography"];
        };
        Label: {
            dissolved_date?: null | components["schemas"]["DateWithPrecision"];
            founded_date?: null | components["schemas"]["DateWithPrecision"];
            founders: number[];
            /** Format: int32 */
            id: number;
            localized_names: components["schemas"]["LocalizedName"][];
            name: string;
        };
        Language: {
            code: string;
            /** Format: int32 */
            id: number;
            name: string;
        };
        LocalizedName: {
            language: components["schemas"]["Language"];
            name: string;
        };
        LocalizedTitle: {
            language: components["schemas"]["Language"];
            title: string;
        };
        Location: {
            city?: string | null;
            country?: string | null;
            province?: string | null;
        };
        Membership: {
            /** Format: int32 */
            artist_id: number;
            roles?: components["schemas"]["CreditRoleRef"][];
            tenure?: components["schemas"]["Tenure"][];
        };
        Message: {
            message: string;
            /** @enum {string} */
            status: "Ok";
        };
        NewArtist: {
            /** @description List of id of artist aliases */
            aliases?: number[] | null;
            artist_type: components["schemas"]["ArtistType"];
            current_location?: null | components["schemas"]["Location"];
            end_date?: null | components["schemas"]["DateWithPrecision"];
            links?: string[] | null;
            localized_names?: components["schemas"]["NewLocalizedName"][] | null;
            /** @description Groups list for individuals, member list for groups, */
            memberships?: components["schemas"]["NewMembership"][] | null;
            name: components["schemas"]["EntityIdent"];
            start_date?: null | components["schemas"]["DateWithPrecision"];
            start_location?: null | components["schemas"]["Location"];
            /** @description Aliases without own page */
            text_aliases?: components["schemas"]["EntityIdent"][] | null;
        };
        NewCorrection_NewArtist: {
            data: {
                /** @description List of id of artist aliases */
                aliases?: number[] | null;
                artist_type: components["schemas"]["ArtistType"];
                current_location?: null | components["schemas"]["Location"];
                end_date?: null | components["schemas"]["DateWithPrecision"];
                links?: string[] | null;
                localized_names?: components["schemas"]["NewLocalizedName"][] | null;
                /** @description Groups list for individuals, member list for groups, */
                memberships?: components["schemas"]["NewMembership"][] | null;
                name: components["schemas"]["EntityIdent"];
                start_date?: null | components["schemas"]["DateWithPrecision"];
                start_location?: null | components["schemas"]["Location"];
                /** @description Aliases without own page */
                text_aliases?: components["schemas"]["EntityIdent"][] | null;
            };
            description: string;
            type: components["schemas"]["CorrectionType"];
        };
        NewCorrection_NewCreditRole: {
            data: {
                description?: string | null;
                name: components["schemas"]["EntityIdent"];
                short_description?: string | null;
                super_roles?: number[] | null;
            };
            description: string;
            type: components["schemas"]["CorrectionType"];
        };
        NewCorrection_NewEvent: {
            data: {
                alternative_names?: string[] | null;
                description?: string | null;
                end_date?: null | components["schemas"]["DateWithPrecision"];
                name: components["schemas"]["EntityIdent"];
                short_description?: string | null;
                start_date?: null | components["schemas"]["DateWithPrecision"];
            };
            description: string;
            type: components["schemas"]["CorrectionType"];
        };
        NewCorrection_NewLabel: {
            data: {
                dissolved_date?: null | components["schemas"]["DateWithPrecision"];
                founded_date?: null | components["schemas"]["DateWithPrecision"];
                founders?: number[] | null;
                localized_names?: components["schemas"]["NewLocalizedName"][] | null;
                name: components["schemas"]["EntityIdent"];
            };
            description: string;
            type: components["schemas"]["CorrectionType"];
        };
        NewCorrection_NewRelease: {
            data: {
                artists: number[];
                catalog_nums: components["schemas"]["CatalogNumber"][];
                credits: components["schemas"]["NewCredit"][];
                events: number[];
                localized_titles: components["schemas"]["NewLocalizedTitle"][];
                recording_end_date?: null | components["schemas"]["DateWithPrecision"];
                recording_start_date?: null | components["schemas"]["DateWithPrecision"];
                release_date?: null | components["schemas"]["DateWithPrecision"];
                release_type: components["schemas"]["ReleaseType"];
                title: string;
                tracks: components["schemas"]["NewTrack"][];
            };
            description: string;
            type: components["schemas"]["CorrectionType"];
        };
        NewCorrection_NewSong: {
            data: {
                credits?: components["schemas"]["NewSongCredit"][] | null;
                languages?: number[] | null;
                localized_titles?: components["schemas"]["NewLocalizedName"][] | null;
                title: components["schemas"]["EntityIdent"];
            };
            description: string;
            type: components["schemas"]["CorrectionType"];
        };
        NewCorrection_NewSongLyrics: {
            data: {
                content: string;
                is_main: boolean;
                /** Format: int32 */
                language_id: number;
                /** Format: int32 */
                song_id: number;
            };
            description: string;
            type: components["schemas"]["CorrectionType"];
        };
        NewCorrection_NewTag: {
            data: {
                alt_names?: string[] | null;
                description?: string | null;
                name: components["schemas"]["EntityIdent"];
                relations?: components["schemas"]["NewTagRelation"][] | null;
                short_description?: string | null;
                type: components["schemas"]["TagType"];
            };
            description: string;
            type: components["schemas"]["CorrectionType"];
        };
        NewCredit: {
            /** Format: int32 */
            artist_id: number;
            on?: number[] | null;
            /** Format: int32 */
            role_id: number;
        };
        NewCreditRole: {
            description?: string | null;
            name: components["schemas"]["EntityIdent"];
            short_description?: string | null;
            super_roles?: number[] | null;
        };
        NewEvent: {
            alternative_names?: string[] | null;
            description?: string | null;
            end_date?: null | components["schemas"]["DateWithPrecision"];
            name: components["schemas"]["EntityIdent"];
            short_description?: string | null;
            start_date?: null | components["schemas"]["DateWithPrecision"];
        };
        NewLabel: {
            dissolved_date?: null | components["schemas"]["DateWithPrecision"];
            founded_date?: null | components["schemas"]["DateWithPrecision"];
            founders?: number[] | null;
            localized_names?: components["schemas"]["NewLocalizedName"][] | null;
            name: components["schemas"]["EntityIdent"];
        };
        NewLocalizedName: {
            /** Format: int32 */
            language_id: number;
            name: string;
        };
        NewLocalizedTitle: {
            /** Format: int32 */
            language_id: number;
            title: string;
        };
        NewMembership: {
            /** Format: int32 */
            artist_id: number;
            roles: number[];
            tenure: components["schemas"]["Tenure"][];
        };
        NewRelease: {
            artists: number[];
            catalog_nums: components["schemas"]["CatalogNumber"][];
            credits: components["schemas"]["NewCredit"][];
            events: number[];
            localized_titles: components["schemas"]["NewLocalizedTitle"][];
            recording_end_date?: null | components["schemas"]["DateWithPrecision"];
            recording_start_date?: null | components["schemas"]["DateWithPrecision"];
            release_date?: null | components["schemas"]["DateWithPrecision"];
            release_type: components["schemas"]["ReleaseType"];
            title: string;
            tracks: components["schemas"]["NewTrack"][];
        };
        NewSong: {
            credits?: components["schemas"]["NewSongCredit"][] | null;
            languages?: number[] | null;
            localized_titles?: components["schemas"]["NewLocalizedName"][] | null;
            title: components["schemas"]["EntityIdent"];
        };
        NewSongCredit: {
            /** Format: int32 */
            artist_id: number;
            /** Format: int32 */
            role_id: number;
        };
        NewSongLyrics: {
            content: string;
            is_main: boolean;
            /** Format: int32 */
            language_id: number;
            /** Format: int32 */
            song_id: number;
        };
        NewTag: {
            alt_names?: string[] | null;
            description?: string | null;
            name: components["schemas"]["EntityIdent"];
            relations?: components["schemas"]["NewTagRelation"][] | null;
            short_description?: string | null;
            type: components["schemas"]["TagType"];
        };
        NewTagRelation: {
            /** Format: int32 */
            related_tag_id: number;
            type: components["schemas"]["TagRelationType"];
        };
        NewTrack: {
            Linked: {
                artists: number[];
                display_title?: string | null;
                duration?: string | null;
                /** Format: int32 */
                song_id: number;
                track_number?: string | null;
            };
        } | {
            Unlinked: {
                artists: number[];
                display_title: string;
                duration?: string | null;
                track_number?: string | null;
            };
        };
        Paginated_Credit: {
            items: {
                artist: components["schemas"]["ArtistReleaseArtist"][];
                cover_url?: string | null;
                release_date?: null | components["schemas"]["DateWithPrecision"];
                release_type: components["schemas"]["ReleaseType"];
                roles: components["schemas"]["CreditRoleRef"][];
                title: string;
            }[];
            /** Format: int32 */
            next_cursor?: number | null;
        };
        Paginated_Discography: {
            items: {
                artist: components["schemas"]["ArtistReleaseArtist"][];
                cover_url?: string | null;
                release_date?: null | components["schemas"]["DateWithPrecision"];
                release_type: components["schemas"]["ReleaseType"];
                title: string;
            }[];
            /** Format: int32 */
            next_cursor?: number | null;
        };
        Release: {
            artists?: components["schemas"]["ReleaseArtist"][];
            catalog_nums?: components["schemas"]["CatalogNumber"][];
            cover_art_url?: string | null;
            credits?: components["schemas"]["ReleaseCredit"][];
            /** Format: int32 */
            id: number;
            localized_titles?: components["schemas"]["LocalizedTitle"][];
            /** Format: date */
            recording_date_end?: string | null;
            recording_date_end_precision?: null | components["schemas"]["DatePrecision"];
            /** Format: date */
            recording_date_start?: string | null;
            recording_date_start_precision?: null | components["schemas"]["DatePrecision"];
            /** Format: date */
            release_date?: string | null;
            release_date_precision?: null | components["schemas"]["DatePrecision"];
            release_type: components["schemas"]["ReleaseType"];
            title: string;
            tracks?: number[];
        };
        ReleaseArtist: {
            /** Format: int32 */
            id: number;
            name: string;
        };
        ReleaseCoverArtFormData: {
            /** Format: binary */
            data: string;
        };
        ReleaseCredit: {
            artist: components["schemas"]["ReleaseArtist"];
            on?: number[] | null;
            role: components["schemas"]["CreditRoleRef"];
        };
        /** @enum {string} */
        ReleaseType: "Album" | "Ep" | "Single" | "Compilation" | "Demo" | "Other";
        SimpleArtist: {
            /** Format: int32 */
            id: number;
            name: string;
        };
        SimpleRelease: {
            cover_art_url?: string | null;
            /** Format: int32 */
            id: number;
            title: string;
        };
        Song: {
            artists?: components["schemas"]["SimpleArtist"][];
            credits?: components["schemas"]["SongCredit"][];
            /** Format: int32 */
            id: number;
            languages?: components["schemas"]["Language"][];
            localized_titles?: components["schemas"]["LocalizedTitle"][];
            lyrics?: components["schemas"]["SongLyrics"][];
            releases?: components["schemas"]["SimpleRelease"][];
            title: string;
        };
        SongCredit: {
            artist: components["schemas"]["SimpleArtist"];
            role: components["schemas"]["CreditRoleRef"];
        };
        SongLyrics: {
            content: string;
            /** Format: int32 */
            id: number;
            is_main: boolean;
            language: components["schemas"]["Language"];
            /** Format: int32 */
            song_id: number;
        };
        Tag: {
            alt_names: components["schemas"]["AlternativeName"][];
            description?: string | null;
            /** Format: int32 */
            id: number;
            name: string;
            relations: components["schemas"]["TagRelation"][];
            short_description?: string | null;
            type: components["schemas"]["TagType"];
        };
        TagRelation: {
            /** Format: int32 */
            related_tag_id: number;
            type: components["schemas"]["TagRelationType"];
        };
        /** @enum {string} */
        TagRelationType: "Inherit" | "Derive";
        /** @enum {string} */
        TagType: "Descriptor" | "Genre" | "Movement" | "Scene";
        Tenure: {
            /** Format: int32 */
            join_year?: number | null;
            /** Format: int32 */
            leave_year?: number | null;
        };
        UploadAvatar: {
            /** Format: binary */
            data: string;
        };
        UploadProfileBanner: {
            /** Format: binary */
            data: string;
        };
        UserProfile: {
            /** @description Avatar url with sub directory, eg. ab/cd/abcd..xyz.jpg */
            avatar_url?: string | null;
            /** @description Banner url with sub directory, eg. ab/cd/abcd..xyz.jpg */
            banner_url?: string | null;
            bio?: string | null;
            /** @description Whether the querist follows the user. Return `None` if querist is not signed in or it's querist's own profile */
            is_following?: boolean | null;
            /** Format: date-time */
            last_login: string;
            name: string;
            roles?: components["schemas"]["UserRole"][];
        };
        UserRole: {
            /** Format: int32 */
            id: number;
            name: components["schemas"]["UserRoleEnum"];
        };
        /** @enum {string} */
        UserRoleEnum: "Admin" | "Moderator" | "User";
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
};
export type AlternativeName = components['schemas']['AlternativeName'];
export type Artist = components['schemas']['Artist'];
export type ArtistReleaseArtist = components['schemas']['ArtistReleaseArtist'];
export type ArtistType = components['schemas']['ArtistType'];
export type AuthCredential = components['schemas']['AuthCredential'];
export type CatalogNumber = components['schemas']['CatalogNumber'];
export type CorrectionType = components['schemas']['CorrectionType'];
export type CreditRole = components['schemas']['CreditRole'];
export type CreditRoleRef = components['schemas']['CreditRoleRef'];
export type CreditRoleSummary = components['schemas']['CreditRoleSummary'];
export type DataEvent = components['schemas']['Data_Event'];
export type DataOptionI32 = components['schemas']['Data_Option_i32'];
export type DataInitDiscography = components['schemas']['DataInitDiscography'];
export type DataOptionArtist = components['schemas']['DataOptionArtist'];
export type DataOptionCreditRole = components['schemas']['DataOptionCreditRole'];
export type DataOptionLabel = components['schemas']['DataOptionLabel'];
export type DataOptionRelease = components['schemas']['DataOptionRelease'];
export type DataOptionSong = components['schemas']['DataOptionSong'];
export type DataOptionSongLyrics = components['schemas']['DataOptionSongLyrics'];
export type DataOptionTag = components['schemas']['DataOptionTag'];
export type DataPaginatedAppearance = components['schemas']['DataPaginatedAppearance'];
export type DataPaginatedCredit = components['schemas']['DataPaginatedCredit'];
export type DataPaginatedDiscography = components['schemas']['DataPaginatedDiscography'];
export type DataUserProfile = components['schemas']['DataUserProfile'];
export type DataVecArtist = components['schemas']['DataVecArtist'];
export type DataVecCreditRoleSummary = components['schemas']['DataVecCreditRoleSummary'];
export type DataVecEvent = components['schemas']['DataVecEvent'];
export type DataVecLabel = components['schemas']['DataVecLabel'];
export type DataVecLanguage = components['schemas']['DataVecLanguage'];
export type DataVecRelease = components['schemas']['DataVecRelease'];
export type DataVecSong = components['schemas']['DataVecSong'];
export type DataVecSongLyrics = components['schemas']['DataVecSongLyrics'];
export type DataVecTag = components['schemas']['DataVecTag'];
export type DataVecUserRole = components['schemas']['DataVecUserRole'];
export type DatePrecision = components['schemas']['DatePrecision'];
export type DateWithPrecision = components['schemas']['DateWithPrecision'];
export type EntityIdent = components['schemas']['EntityIdent'];
export type Event = components['schemas']['Event'];
export type HandleCorrectionMethod = components['schemas']['HandleCorrectionMethod'];
export type InitDiscography = components['schemas']['InitDiscography'];
export type Label = components['schemas']['Label'];
export type Language = components['schemas']['Language'];
export type LocalizedName = components['schemas']['LocalizedName'];
export type LocalizedTitle = components['schemas']['LocalizedTitle'];
export type Location = components['schemas']['Location'];
export type Membership = components['schemas']['Membership'];
export type Message = components['schemas']['Message'];
export type NewArtist = components['schemas']['NewArtist'];
export type NewCorrectionNewArtist = components['schemas']['NewCorrection_NewArtist'];
export type NewCorrectionNewCreditRole = components['schemas']['NewCorrection_NewCreditRole'];
export type NewCorrectionNewEvent = components['schemas']['NewCorrection_NewEvent'];
export type NewCorrectionNewLabel = components['schemas']['NewCorrection_NewLabel'];
export type NewCorrectionNewRelease = components['schemas']['NewCorrection_NewRelease'];
export type NewCorrectionNewSong = components['schemas']['NewCorrection_NewSong'];
export type NewCorrectionNewSongLyrics = components['schemas']['NewCorrection_NewSongLyrics'];
export type NewCorrectionNewTag = components['schemas']['NewCorrection_NewTag'];
export type NewCredit = components['schemas']['NewCredit'];
export type NewCreditRole = components['schemas']['NewCreditRole'];
export type NewEvent = components['schemas']['NewEvent'];
export type NewLabel = components['schemas']['NewLabel'];
export type NewLocalizedName = components['schemas']['NewLocalizedName'];
export type NewLocalizedTitle = components['schemas']['NewLocalizedTitle'];
export type NewMembership = components['schemas']['NewMembership'];
export type NewRelease = components['schemas']['NewRelease'];
export type NewSong = components['schemas']['NewSong'];
export type NewSongCredit = components['schemas']['NewSongCredit'];
export type NewSongLyrics = components['schemas']['NewSongLyrics'];
export type NewTag = components['schemas']['NewTag'];
export type NewTagRelation = components['schemas']['NewTagRelation'];
export type NewTrack = components['schemas']['NewTrack'];
export type PaginatedCredit = components['schemas']['Paginated_Credit'];
export type PaginatedDiscography = components['schemas']['Paginated_Discography'];
export type Release = components['schemas']['Release'];
export type ReleaseArtist = components['schemas']['ReleaseArtist'];
export type ReleaseCoverArtFormData = components['schemas']['ReleaseCoverArtFormData'];
export type ReleaseCredit = components['schemas']['ReleaseCredit'];
export type ReleaseType = components['schemas']['ReleaseType'];
export type SimpleArtist = components['schemas']['SimpleArtist'];
export type SimpleRelease = components['schemas']['SimpleRelease'];
export type Song = components['schemas']['Song'];
export type SongCredit = components['schemas']['SongCredit'];
export type SongLyrics = components['schemas']['SongLyrics'];
export type Tag = components['schemas']['Tag'];
export type TagRelation = components['schemas']['TagRelation'];
export type TagRelationType = components['schemas']['TagRelationType'];
export type TagType = components['schemas']['TagType'];
export type Tenure = components['schemas']['Tenure'];
export type UploadAvatar = components['schemas']['UploadAvatar'];
export type UploadProfileBanner = components['schemas']['UploadProfileBanner'];
export type UserProfile = components['schemas']['UserProfile'];
export type UserRole = components['schemas']['UserRole'];
export type UserRoleEnum = components['schemas']['UserRoleEnum'];
export type $defs = Record<string, never>;
export interface operations {
    pending_correction: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                entity_type: "artist" | "label" | "release" | "song" | "tag" | "event" | "song-lyrics" | "credit-role";
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Data_Option_i32"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    find_many_artist: {
        parameters: {
            query: {
                artist_type?: components["schemas"]["ArtistType"][];
                exclusion?: number[];
                keyword: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecArtist"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    create_artist: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewArtist"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_artist_by_id: {
        parameters: {
            query?: {
                artist_type?: components["schemas"]["ArtistType"][];
                exclusion?: number[];
            };
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataOptionArtist"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    upsert_artist_correction: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewArtist"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_artist_apperances: {
        parameters: {
            query: {
                cursor: number;
                limit: number;
            };
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataPaginatedAppearance"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    get_artist_credits: {
        parameters: {
            query: {
                cursor: number;
                limit: number;
            };
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataPaginatedCredit"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    find_artist_discographies_by_type: {
        parameters: {
            query: {
                cursor: number;
                limit: number;
                release_type: components["schemas"]["ReleaseType"];
            };
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataPaginatedDiscography"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    find_artist_discographies_init: {
        parameters: {
            query: {
                limit: number;
            };
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataInitDiscography"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    upload_artist_profile_image: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    upload_avatar: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "multipart/form-data": components["schemas"]["UploadAvatar"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    handle_correction: {
        parameters: {
            query: {
                method: components["schemas"]["HandleCorrectionMethod"];
            };
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    create_credit_role: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewCreditRole"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_credit_role_by_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataOptionCreditRole"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    upsert_credit_role_correction: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewCreditRole"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_many_credit_roles_summary: {
        parameters: {
            query: {
                keyword: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecCreditRoleSummary"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    find_event_by_keyword: {
        parameters: {
            query: {
                keyword: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecEvent"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    create: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewEvent"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_event_by_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Data_Event"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    upsert_correction: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewEvent"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    health_check: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_label_by_keyword: {
        parameters: {
            query: {
                keyword: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecLabel"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    create_label: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewLabel"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_label_by_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataOptionLabel"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    upsert_label_correction: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewLabel"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    language_list: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecLanguage"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    profile: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataUserProfile"];
                };
            };
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    upload_profile_banner: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "multipart/form-data": components["schemas"]["UploadProfileBanner"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    profile_with_name: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                name: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataUserProfile"];
                };
            };
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    update_bio: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "text/plain": string;
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    find_release_by_keyword: {
        parameters: {
            query: {
                keyword: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecRelease"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    create_release: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewRelease"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_release_by_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataOptionRelease"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    update_release: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewRelease"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    upload_release_cover_art: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ReleaseCoverArtFormData"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    sign_in: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AuthCredential"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataUserProfile"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    sign_out: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    sign_up: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AuthCredential"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataUserProfile"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    find_song_by_keyword: {
        parameters: {
            query: {
                keyword: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecSong"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    create_song: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewSong"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_one_song_lyrics: {
        parameters: {
            query: {
                language_id: number;
                song_id: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataOptionSongLyrics"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    create_song_lyrics: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewSongLyrics"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    update_song_lyrics: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewSongLyrics"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_many_song_lyrics: {
        parameters: {
            query: {
                song_id: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecSongLyrics"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    find_song_by_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataOptionSong"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    update_song: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewSong"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    find_tag_by_keyword: {
        parameters: {
            query: {
                keyword: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecTag"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    create_tag: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewTag"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    find_tag_by_id: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataOptionTag"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
    upsert_tag_correction: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewCorrection_NewTag"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    user_roles: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DataVecUserRole"];
                };
            };
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        message: string;
                        /** @enum {string} */
                        status: "Err";
                    };
                };
            };
        };
    };
}
export enum ApiPaths {
    find_many_artist = "/artist",
    create_artist = "/artist",
    find_artist_by_id = "/artist/{id}",
    upsert_artist_correction = "/artist/{id}",
    find_artist_apperances = "/artist/{id}/appearances",
    get_artist_credits = "/artist/{id}/credits",
    find_artist_discographies_by_type = "/artist/{id}/discographies",
    find_artist_discographies_init = "/artist/{id}/discographies/init",
    upload_artist_profile_image = "/artist/{id}/profile-image",
    upload_avatar = "/avatar",
    handle_correction = "/correction/{id}",
    create_credit_role = "/credit-role",
    find_many_credit_roles_summary = "/credit-role/summary",
    find_credit_role_by_id = "/credit-role/{id}",
    upsert_credit_role_correction = "/credit-role/{id}",
    find_event_by_keyword = "/event",
    create = "/event",
    find_event_by_id = "/event/{id}",
    upsert_correction = "/event/{id}",
    health_check = "/health_check",
    find_label_by_keyword = "/label",
    create_label = "/label",
    find_label_by_id = "/label/{id}",
    upsert_label_correction = "/label/{id}",
    language_list = "/languages",
    profile = "/profile",
    upload_profile_banner = "/profile-banner",
    update_bio = "/profile/bio",
    profile_with_name = "/profile/{name}",
    find_release_by_keyword = "/release",
    create_release = "/release",
    find_release_by_id = "/release/{id}",
    update_release = "/release/{id}",
    upload_release_cover_art = "/release/{id}/cover-art",
    sign_in = "/sign-in",
    sign_out = "/sign-out",
    sign_up = "/sign-up",
    find_song_by_keyword = "/song",
    create_song = "/song",
    find_one_song_lyrics = "/song-lyrics",
    create_song_lyrics = "/song-lyrics",
    find_many_song_lyrics = "/song-lyrics/many",
    update_song_lyrics = "/song-lyrics/{id}",
    find_song_by_id = "/song/{id}",
    update_song = "/song/{id}",
    find_tag_by_keyword = "/tag",
    create_tag = "/tag",
    find_tag_by_id = "/tag/{id}",
    upsert_tag_correction = "/tag/{id}",
    user_roles = "/user-roles",
    pending_correction = "/{entity_type}/{id}/pending-correction"
}
