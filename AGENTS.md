# Repository Guidelines

## Project Structure & Module Organization

- App code in `src/` (Solid + TypeScript): `components/`, `routes/` and `route/`, `views/`, `api/`, `state/`, `utils/`, `locale/`, `assets/`.
- Build output in `dist/`; static assets in `public/`.
- Monorepo: workspace packages under `packages/` (e.g., `packages/toolkit`).
- Storybook config in `.storybook/`. Generated files: `src/routeTree.gen.ts`, `src/**/openapi.ts` (don’t edit).

## Build, Test, and Development Commands

- Install: `pnpm i`
- Storybook: `pnpm run storybook` (dev) / `pnpm run build-storybook` (static build).
- Lint/format (via Just): `just lint` (oxlint + eslint), `just fix`, `just fmt`.
- Tests (Vitest workspace): `pnpm exec vitest run` (browser mode configured in `vitest.workspace.ts`). Place DOM tests under `test/dom/**/*.test.tsx`.
- i18n: `just mod i18n extract` then `just mod i18n compile` when adding/changing strings.
- Type check: `pnpm tsc -p .`

## Coding Style & Naming Conventions

- Use let to declare variables and lambdas, use function delaration for top level functions, use const on constants.
- Language: TypeScript/TSX (Solid). Components in PascalCase (e.g., `src/components/MyWidget.tsx`); utilities in camelCase.
- Indentation: tabs; semicolons: off; one attribute per JSX line.
- Imports: prefer `import type { X }` for types; auto‑sorted by Prettier.
- Styling: Tailwind CSS; shared styles in `src/style/` and `src/index.css`.
- Solid: do not destructure props; follow reactive rules and a11y linting.
- i18n: Prefer `<Trans>` for JSX text; use `t` only when you need a translated string value (see below).

### Typescript

- Use strict TypeScript, avoid `any`, prefer explicit return types
- Prefer using type over inferfaces

## Internationalization (lingui‑solid)

- Preferred usage: 通常使用 `<Trans>` 包裹 JSX 文本；仅在需要“获得翻译后的字符串”时使用 `t`（如属性、变量、标题、通知等）。
- Imports: `import { Trans, t, useLingui } from "@lingui-solid/solid/macro"`
- JSX text with `<Trans>` (supports nested tags/expressions):

  ```tsx
  import { Trans } from "@lingui-solid/solid/macro"

  const LinkText = () => (
  	<p>
  		<Trans>
  			See <a href="https://lingui.dev">documentation</a> for details
  		</Trans>
  	</p>
  )
  ```

- String value with `t` (attributes/variables, not JSX content):

  ```tsx
  import { t } from "@lingui-solid/solid/macro"

  const label = t`Delete`
  export const Button = () => (
  	<button
  		aria-label={label}
  		title={t`Delete item`}
  	>
  		×
  	</button>
  )
  ```

- Do not mix: 不要在同一句里同时用 `<Trans>` 与 `t`；不要翻译动态数据本身，使用表达式插入：`<Trans>Hello {name}</Trans>`。
- Update catalogs: 新增/改动文案后运行 `just i18n extract` 与 `just i18n compile`。

## Testing Guidelines

- Framework: Vitest + Playwright browser; `@solidjs/testing-library` and `@testing-library/jest-dom` available.
- Location: `test/dom/**/*.{test,vitest}.(ts|tsx)`.
- Keep tests isolated and deterministic; prefer user-facing queries and `data-testid` when needed.

## Commit & Pull Request Guidelines

- Messages: short, imperative summary (e.g., “Refactor form validation”, “Fix: getValues null case”). Reference issues like `#123`.
- PRs: clear description, linked issues, screenshots/GIFs for UI changes.
- Checks: ensure `pnpm build`, `just lint`, and `pnpm check` (Prettier) pass; update i18n catalogs if strings changed.
- Scope PRs narrowly; avoid committing `.env*`, build artifacts, or generated files.

## Security & Configuration Tips

- Required env: set `VITE_SERVER_URL` in `.env.local` for local dev; never commit secrets.
- Dev server proxies `/api` to `VITE_SERVER_URL`.
