{"name": "@touhouclouddb/web", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "serve": "vite preview", "check": "prettier --check ./src", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@floating-ui/dom": "^1.7.3", "@formisch/solid": "^0.3.0", "@kobalte/core": "^0.13.11", "@lingui-solid/solid": "^5.1.4", "@modular-forms/solid": "^0.25.1", "@shikijs/markdown-it": "^3.11.0", "@shikijs/transformers": "^3.11.0", "@solid-primitives/destructure": "^0.2.2", "@solid-primitives/i18n": "^2.2.1", "@solidjs/meta": "^0.29.4", "@solidjs/router": "^0.15.3", "@tanstack/solid-query": "^5.85.5", "@tanstack/solid-query-devtools": "^5.85.5", "@tanstack/solid-router": "^1.131.27", "@thc/api": "workspace:*", "@thc/icons": "workspace:*", "@thc/toolkit": "workspace:*", "arktype": "^2.1.20", "cropperjs": "^2.0.1", "dayjs": "^1.11.13", "effect": "^3.17.8", "fast-check": "^4.2.0", "marked": "^16.2.0", "marked-footnote": "^1.4.0", "marked-shiki": "^1.2.1", "openapi-fetch": "^0.14.0", "shiki": "^3.11.0", "solid-cropper": "workspace:*", "solid-floating-ui": "^0.3.1", "solid-js": "^1.9.9", "solid-popper": "^0.3.0", "solid-radix-icons": "^1.0.0", "tailwind-merge": "^3.3.1", "valibot": "1.1.0"}, "devDependencies": {"@ark/attest": "^0.48.2", "@chromatic-com/storybook": "^4.1.1", "@eslint/js": "^9.33.0", "@faker-js/faker": "^9.9.0", "@lingui-solid/babel-plugin-extract-messages": "^5.1.3", "@lingui-solid/babel-plugin-lingui-macro": "^5.1.3", "@lingui-solid/vite-plugin": "^5.1.3", "@lingui/cli": "^5.4.1", "@lingui/conf": "^5.4.1", "@lingui/core": "^5.4.1", "@lingui/macro": "^5.4.1", "@solidjs/testing-library": "^0.8.10", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/test": "^8.6.14", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/vite": "^4.1.12", "@tanstack/eslint-plugin-query": "^5.83.1", "@tanstack/router-plugin": "^1.131.27", "@tanstack/solid-router-devtools": "^1.131.27", "@testing-library/jest-dom": "^6.7.0", "@testing-library/user-event": "^14.6.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/eslint-config-prettier": "^6.11.3", "@types/markdown-it": "^14.1.2", "@types/markdown-it-footnote": "^3.0.4", "@vitest/browser": "^3.2.4", "babel-preset-solid": "^1.9.9", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-oxlint": "^1.12.0", "eslint-plugin-perfectionist": "^4.15.0", "eslint-plugin-solid": "^0.14.5", "eslint-plugin-storybook": "^9.1.2", "globals": "^16.3.0", "hotscript": "^1.0.13", "husky": "^9.1.7", "jsdom": "^26.1.0", "openapi-typescript": "^7.9.1", "oxlint": "^1.12.0", "playwright": "^1.55.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "solid-devtools": "^0.34.3", "storybook": "^9.1.2", "storybook-solidjs": "1.0.0-beta.7", "storybook-solidjs-vite": "^9.0.3", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "typescript-eslint": "^8.40.0", "vite": "^7.1.3", "vite-plugin-babel-macros": "^1.0.6", "vite-plugin-solid": "^2.11.8", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "trustedDependencies": ["@tailwindcss/oxide", "esbuild"]}